import { ref, computed } from "vue";
import { defineStore } from "pinia";


export interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  avatar?: string;
}

export const useAuthStore = defineStore("auth", () => {
  const token = ref<string | null>(localStorage.getItem("token"));
  const user = ref<User | null>(null);

  const isLoggedIn = computed(() => !!token.value);

  // 登出
  const logout = () => {
    token.value = null;
    user.value = null;
    localStorage.removeItem("token");
  };

  // 检查权限
  const hasPermission = (permission: string) => {
    return user.value?.permissions.includes(permission) || false;
  };

  // 检查角色
  const hasRole = (role: string) => {
    return user.value?.role === role;
  };

  return {
    token,
    user,
    isLoggedIn,
    logout,
    hasPermission,
    hasRole,
  };
});
