<script setup lang="ts">
import { RouterView } from "vue-router";
import { useThemeStore } from "@/stores/theme";
import { useLocaleStore } from "@/stores/locale";
import { darkTheme } from "naive-ui";
import { computed } from "vue";
import hljs from "highlight.js/lib/core";

const themeStore = useThemeStore();
const localeStore = useLocaleStore();

// 主题配置
const theme = computed(() => (themeStore.isDark ? darkTheme : null));
hljs.registerLanguage("naive-log", () => ({
  contains: [
    {
      className: "number",
      begin: "",
    },
  ],
}));
// const themeOverrides: GlobalThemeOverrides = {
//   common: {
//     primaryColor: '#2080f0',
//     primaryColorHover: '#4098fc',
//     primaryColorPressed: '#1060c9',
//     primaryColorSuppl: '#4098fc'
//   }
// }
</script>

<template>
  <n-config-provider
    :theme="theme"
    :locale="localeStore.getNaiveUILocale()"
    :date-locale="localeStore.getNaiveUIDateLocale()"
    :hljs="hljs"
  >
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <n-spin :show="themeStore.globalLoading" content-class="global-loading-full">
              <router-view />
            </n-spin>
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<style lang="less">
* {
  margin: 0;
  padding: 0;
}

body,
html,
#app,
.n-config-provider,
.global-loading-full {
  width: 100%;
  height: 100%;
}
</style>

<style lang="less" scoped>
.n-spin-container {
  height: 100%;
}
</style>
