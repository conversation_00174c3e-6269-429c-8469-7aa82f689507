import { ref } from "vue";
import { defineStore } from "pinia";
import { useI18n } from "vue-i18n";
import { zhCN, dateZhCN, enUS, dateEnUS } from "naive-ui";

export const useLocaleStore = defineStore("locale", () => {
  const { locale } = useI18n();

  const toggleLocale = (lang: string = "zh-CN") => {
    locale.value = lang;
  };

  const getNaiveUILocale = () => {
    return locale.value === "zh-CN" ? zhCN : enUS;
  };

  const getNaiveUIDateLocale = () => {
    return locale.value === "zh-CN" ? dateZhCN : dateEnUS;
  };

  return {
    locale,
    toggleLocale,
    getNaiveUILocale,
    getNaiveUIDateLocale,
  };
});
