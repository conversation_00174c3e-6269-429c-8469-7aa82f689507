import { ResponseData } from '@/utils/request'

// Mock handler for DELETE /user-delete
export default function (params: any): ResponseData<any> {
  // Validate required fields
  if (!params.id) {
    return {
      code: 400,
      data: null,
      message: 'User ID is required',
      success: false
    }
  }
  
  // Simulate successful user deletion
  return {
    code: 200,
    data: null,
    message: 'User deleted successfully',
    success: true
  }
}