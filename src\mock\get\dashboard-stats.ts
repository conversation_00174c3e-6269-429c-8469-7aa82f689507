import { ResponseData } from '@/utils/request'

// Mock handler for GET /dashboard-stats
export default function (): ResponseData<any> {
  return {
    code: 200,
    data: {
      // Statistics cards data
      stats: [
        {
          title: 'Total Users',
          value: 8846,
          suffix: '',
          icon: 'user',
          color: '#2080f0',
          increase: 12.3
        },
        {
          title: 'Total Revenue',
          value: 126560,
          prefix: '$',
          icon: 'money',
          color: '#18a058',
          increase: 5.2
        },
        {
          title: 'Total Orders',
          value: 6721,
          suffix: '',
          icon: 'shopping-cart',
          color: '#f0a020',
          increase: 8.1
        },
        {
          title: 'Conversion Rate',
          value: 4.28,
          suffix: '%',
          icon: 'chart',
          color: '#d03050',
          increase: -2.5
        }
      ],
      
      // Visit trend data for chart
      visitTrend: {
        xAxis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        series: [
          {
            name: 'Visitors',
            data: [820, 932, 901, 934, 1290, 1330, 1320]
          },
          {
            name: 'Page Views',
            data: [420, 532, 501, 534, 790, 1130, 1220]
          }
        ]
      },
      
      // Sales trend data for chart
      salesTrend: {
        xAxis: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        series: [
          {
            name: 'Last Year',
            data: [4500, 5000, 6500, 7800, 8900, 9000, 10500, 11500, 12000, 12200, 13100, 15000]
          },
          {
            name: 'This Year',
            data: [5500, 6000, 7000, 8500, 9900, 11000, 12500, 13500, 14000, 15200, 16100, 17000]
          }
        ]
      },
      
      // Recent orders for table
      recentOrders: [
        {
          id: 'ORD-2023-1001',
          customer: 'John Doe',
          product: 'Premium Subscription',
          amount: 199.99,
          status: 'completed',
          date: '2023-06-15 14:23:02'
        },
        {
          id: 'ORD-2023-1002',
          customer: 'Jane Smith',
          product: 'Basic Subscription',
          amount: 99.99,
          status: 'processing',
          date: '2023-06-15 13:45:18'
        },
        {
          id: 'ORD-2023-1003',
          customer: 'Robert Johnson',
          product: 'Enterprise Package',
          amount: 599.99,
          status: 'completed',
          date: '2023-06-15 11:32:45'
        },
        {
          id: 'ORD-2023-1004',
          customer: 'Emily Davis',
          product: 'Premium Subscription',
          amount: 199.99,
          status: 'pending',
          date: '2023-06-15 10:15:30'
        },
        {
          id: 'ORD-2023-1005',
          customer: 'Michael Wilson',
          product: 'Basic Subscription',
          amount: 99.99,
          status: 'completed',
          date: '2023-06-15 09:05:12'
        }
      ]
    },
    message: 'Success',
    success: true
  }
}