import { ResponseData } from '@/utils/request'

// Mock handler for PUT /user-update
export default function (data: any): ResponseData<any> {
  // Validate required fields
  if (!data.id) {
    return {
      code: 400,
      data: null,
      message: 'User ID is required',
      success: false
    }
  }
  
  // Simulate successful user update
  return {
    code: 200,
    data: {
      ...data,
      updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    },
    message: 'User updated successfully',
    success: true
  }
}