import { ref } from "vue";
import { defineStore } from "pinia";

export const useThemeStore = defineStore("theme", () => {
  const isDark = ref(false);

  // 初始化主题，跟随系统
  const initTheme = () => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    isDark.value = mediaQuery.matches;

    // 监听系统主题变化
    mediaQuery.addEventListener("change", (e) => {
      isDark.value = e.matches;
    });
  };

  const toggleTheme = () => {
    isDark.value = !isDark.value;
  };

  // 全局加载开关
  const globalLoading = ref(false);
  const toggleGlobalLoading = (loading: boolean) => {
    globalLoading.value = loading;
  };

  return {
    isDark,
    initTheme,
    toggleTheme,
    globalLoading,
    toggleGlobalLoading,
  };
});
