import { ResponseData } from '@/utils/request'

// Mock handler for POST /user-create
export default function (data: any): ResponseData<any> {
  // Validate required fields
  if (!data.username || !data.name || !data.email || !data.role) {
    return {
      code: 400,
      data: null,
      message: 'Missing required fields',
      success: false
    }
  }
  
  // Simulate successful user creation
  return {
    code: 200,
    data: {
      id: Math.floor(Math.random() * 1000) + 10, // Generate random ID
      ...data,
      status: data.status || 1,
      createTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    },
    message: 'User created successfully',
    success: true
  }
}