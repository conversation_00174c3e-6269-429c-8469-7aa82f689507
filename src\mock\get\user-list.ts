import { ResponseData } from '@/utils/request'

// Mock user data
const users = [
  {
    id: 1,
    username: 'admin',
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '************',
    role: 'admin',
    status: 1,
    createTime: '2023-01-01 00:00:00'
  },
  {
    id: 2,
    username: 'editor',
    name: 'Editor User',
    email: '<EMAIL>',
    phone: '************',
    role: 'editor',
    status: 1,
    createTime: '2023-01-02 00:00:00'
  },
  {
    id: 3,
    username: 'user',
    name: 'Normal User',
    email: '<EMAIL>',
    phone: '************',
    role: 'user',
    status: 1,
    createTime: '2023-01-03 00:00:00'
  },
  {
    id: 4,
    username: 'guest',
    name: 'Guest User',
    email: '<EMAIL>',
    phone: '************',
    role: 'guest',
    status: 0,
    createTime: '2023-01-04 00:00:00'
  },
  {
    id: 5,
    username: 'test',
    name: 'Test User',
    email: '<EMAIL>',
    phone: '************',
    role: 'user',
    status: 1,
    createTime: '2023-01-05 00:00:00'
  }
]

// Mock handler for GET /user-list
export default function (params: any): ResponseData<any> {
  // Filter users based on query parameters
  let result = [...users]
  
  if (params) {
    if (params.username) {
      result = result.filter(user => user.username.includes(params.username))
    }
    if (params.name) {
      result = result.filter(user => user.name.includes(params.name))
    }
    if (params.role !== undefined) {
      result = result.filter(user => user.role === params.role)
    }
    if (params.status !== undefined) {
      result = result.filter(user => user.status === parseInt(params.status))
    }
  }
  
  // Pagination
  const page = parseInt(params?.page || '1')
  const pageSize = parseInt(params?.pageSize || '10')
  const total = result.length
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const data = result.slice(start, end)
  
  return {
    code: 200,
    data: {
      list: data,
      pagination: {
        total,
        page,
        pageSize
      }
    },
    message: 'Success',
    success: true
  }
}