
const baseURL = import.meta.env.VITE_COMMON_BASE_API_URL;

export default async (url = "", data: any, type = "GET") => {

  type = type.toUpperCase();
  url = baseURL + url;

  let dataStr = ""; //数据拼接字符串
  Object.keys(data).forEach((key) => {
    dataStr += key + "=" + data[key] + "&";
  });
  if (dataStr !== "") {
    dataStr = dataStr.substring(0, dataStr.lastIndexOf("&"));
    if (type == "GET") {
      url = url + "?" + dataStr;
    }
  }
  let requestConfig: RequestInit = {
    credentials: "same-origin",
    method: type,
    headers: {
      Accept: "application/json",
      "Content-Type": "application/x-www-form-urlencoded",
    },
    mode: "cors", // 用来决定是否允许跨域请求  值有 三个 same-origin，no-cors（默认）以及 cores;
    cache: "no-cache", // 是否缓存请求资源 可选值有 default 、 no-store 、 reload 、 no-cache 、 force-cache 或者 only-if-cached 。
  };

  if (type == "POST") {
    requestConfig.body = dataStr;
  }
  try {
    const response = await fetch(url, requestConfig);
    const responseJson = await response.json();
    console.log(responseJson);
    
    return responseJson;
  } catch (error: any) {
    throw new Error(error);
  }
};
