<script setup lang="ts">
import { ref, reactive } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useThemeStore } from "@/stores/theme";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import type { FormInst } from "naive-ui";
import { getFingerprint } from "@/utils/fingerprint";
import authApi from "@/api/auth";
import { VS_KEY } from "@/api";

const authStore = useAuthStore();
const router = useRouter();
const { t } = useI18n();
const themeStore = useThemeStore();

const model = reactive({
  username: "",
  password: "",
});

const rules = {
  username: {
    required: true,
    message: t("auth.usernameRule"),
    trigger: "blur",
  },
  password: {
    required: true,
    message: t("auth.passwordRule"),
    trigger: "blur",
  },
};

const loginFormRef = ref<FormInst | null>(null);

const handleSubmit = async () => {
  const valid = await loginFormRef.value?.validate();

  if (valid) {
    themeStore.toggleGlobalLoading(true);
    const pass_md5 = CryptoJS.MD5(model.password).toString();
    const token = CryptoJS.SHA1(
      `${model.username}${pass_md5}${VS_KEY}`
    ).toString();
    await authApi.login({
      username: model.username,
      password: pass_md5,
      token: token,
    }).then((res) => {
      themeStore.toggleGlobalLoading(false);
      if (res.code === 200) {
        localStorage.setItem("token", res.data.token);
        localStorage.setItem("user", JSON.stringify(res.data.user));
        router.push("/");
      } else {
        window.$message.error(res.message);
      }
    });
    if (success) {
      router.push("/");
    }
  }
};

// getFingerprint();
</script>

<template>
  <n-layout>
    <n-card title="">
      <template #header-extra> </template>
      <n-h1 style="text-align: center">{{ t("auth.title") }}</n-h1>
      <n-form ref="loginFormRef" :model="model" :rules="rules" size="large">
        <n-form-item path="username" :label="t('auth.username')" size="large">
          <n-input
            v-model:value="model.username"
            @keydown.enter.prevent
            maxlength="32"
          />
        </n-form-item>
        <n-form-item path="password" :label="t('auth.password')" size="large">
          <n-input
            v-model:value="model.password"
            @keydown.enter.prevent
            type="password"
            maxlength="32"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="center">
          <n-button @click="handleSubmit" size="large">登录</n-button>
        </n-space>
      </template>
    </n-card>
  </n-layout>
</template>

<style lang="less" scoped>
.n-layout {
  height: 100%;
}
.n-card {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 400px;
  transform: translate(-50%, -50%);
  border-radius: 10px;
}
</style>
