import { create<PERSON>lova } from "alova";
import adapterFetch from "alova/fetch";

const alovaInstance = createAlova({
  requestAdapter: adapterFetch(),
  responded: (response) => {
    // 开发者模式下打印
    if (import.meta.env.DEV) {
      console.log(response.json());
    }
    return response.json();
  },
  timeout: 50000,
});

export const get = (url: string, params?: any) => {
  return alovaInstance.Get(url, params);
};

export const post = (url: string, data?: any) => {
  return alovaInstance.Post(url, data);
};

export default alovaInstance;
